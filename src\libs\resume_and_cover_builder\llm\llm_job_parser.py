import os
import tempfile
import textwrap
import time
import re  # For email validation
from src.libs.resume_and_cover_builder.utils import LoggerChatModel
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate, PromptTemplate
from langchain_google_genai import Chat<PERSON><PERSON>gleGenerativeAI
from dotenv import load_dotenv
from concurrent.futures import ThreadPoolExecutor, as_completed
from loguru import logger
from pathlib import Path
from langchain_core.prompt_values import StringPromptValue
from langchain_core.runnables import RunnablePassthrough
from langchain_text_splitters import TokenTextSplitter
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain_community.vectorstores import FAISS
from src.libs.resume_and_cover_builder.config import global_config
from langchain_community.document_loaders import TextLoader
from requests.exceptions import HTTPError as HTTPStatusError  # HTTP error handling
import config as cfg

# Load environment variables from the .env file
load_dotenv()

# Configure the log file
log_folder = 'log/resume/gpt_resume'
if not os.path.exists(log_folder):
    os.makedirs(log_folder)
log_path = Path(log_folder).resolve()
logger.add(log_path / "gpt_resume.log", rotation="1 day", compression="zip", retention="7 days", level="DEBUG")


class LLMParser:
    def __init__(self, openai_api_key=None, api_key=None):
        """
        初始化LLMParser，使用Gemini API
        Args:
            openai_api_key: 为了向后兼容保留的参数名
            api_key: Gemini API密钥
        """
        # 向后兼容处理
        if openai_api_key is not None:
            api_key = openai_api_key
        elif api_key is None:
            raise ValueError("必须提供API密钥")
        self.api_key = api_key

        try:
            # 使用Gemini模型，提高温度以增强创造性
            self.llm = LoggerChatModel(
                ChatGoogleGenerativeAI(
                    model="gemini-2.5-flash-preview-05-20",
                    google_api_key=api_key,
                    temperature=1.0,  # 使用最高允许温度以增强创造性和扩展能力
                    request_timeout=60.0,
                    max_retries=3,
                    transport="rest"  # 强制使用REST传输而不是gRPC
                )
            )
            # 暂时禁用embeddings，因为gRPC连接问题
            # self.llm_embeddings = GoogleGenerativeAIEmbeddings(
            #     model="models/embedding-001",
            #     google_api_key=api_key,
            #     request_timeout=60.0,
            #     max_retries=3,
            #     transport="rest"  # 强制使用REST传输而不是gRPC
            # )
            self.llm_embeddings = None  # 禁用embeddings
            logger.info("LLMParser初始化成功，使用Gemini模型 (REST传输)")
            self.fallback_mode = False

        except Exception as e:
            logger.error(f"LLMParser初始化失败: {str(e)}")
            logger.warning("启用降级模式，使用基础文本解析")
            self.llm = None
            self.llm_embeddings = None
            self.fallback_mode = True

        self.vectorstore = None  # Will be initialized after document loading

    @staticmethod
    def _preprocess_template_string(template: str) -> str:
        """
        Preprocess the template string by removing leading whitespaces and indentation.
        Args:
            template (str): The template string to preprocess.
        Returns:
            str: The preprocessed template string.
        """
        return textwrap.dedent(template)
    
    def set_body_html(self, body_html):
        """
        Retrieves the job description from HTML, processes it, and initializes the vectorstore.
        Args:
            body_html (str): The HTML content to process.
        """
        # 保存原始HTML内容用于降级模式
        self.body_html = body_html

        # 如果在降级模式，直接返回，不进行向量化处理
        if self.fallback_mode:
            logger.info("使用降级模式，跳过向量化处理")
            return

        # 暂时跳过向量化处理，因为embeddings的gRPC连接问题
        # 直接使用LLM进行解析，LLM已经配置为使用REST传输
        logger.info("跳过向量化处理，直接使用LLM解析（避免embeddings gRPC问题）")
        return

        # Save the HTML content to a temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".html", mode="w", encoding="utf-8") as temp_file:
            temp_file.write(body_html)
            temp_file_path = temp_file.name
        try:
            loader = TextLoader(temp_file_path, encoding="utf-8", autodetect_encoding=True)
            document = loader.load()
            logger.debug("Document successfully loaded.")
        except Exception as e:
            logger.error(f"Error during document loading: {e}")
            # 在错误情况下启用降级模式
            self.fallback_mode = True
            logger.warning("文档加载失败，启用降级模式")
            return
        finally:
            os.remove(temp_file_path)
            logger.debug(f"Temporary file removed: {temp_file_path}")

        # Split the text into chunks
        try:
            text_splitter = TokenTextSplitter(chunk_size=500, chunk_overlap=50)
            all_splits = text_splitter.split_documents(document)
            logger.debug(f"Text split into {len(all_splits)} fragments.")
        except Exception as e:
            logger.error(f"Error during text splitting: {e}")
            self.fallback_mode = True
            logger.warning("文本分割失败，启用降级模式")
            return

        # Create the vectorstore using FAISS
        try:
            if self.llm_embeddings:
                self.vectorstore = FAISS.from_documents(documents=all_splits, embedding=self.llm_embeddings)
                logger.debug("Vectorstore successfully initialized.")
            else:
                raise Exception("Embeddings not available")
        except Exception as e:
            logger.error(f"Error during vectorstore creation: {e}")
            self.fallback_mode = True
            logger.warning("向量存储创建失败，启用降级模式")

    def _retrieve_context(self, query: str, top_k: int = 3) -> str:
        """
        Retrieves the most relevant text fragments using the retriever.
        Args:
            query (str): The search query.
            top_k (int): Number of fragments to retrieve.
        Returns:
            str: Concatenated text fragments.
        """
        # 由于跳过了向量化处理，直接使用HTML内容
        logger.debug(f"使用HTML内容作为上下文，查询: '{query}'")
        if hasattr(self, 'body_html') and self.body_html:
            # 清理HTML并限制内容长度以避免token限制
            import re
            from html import unescape

            # 移除HTML标签
            text = re.sub(r'<[^>]+>', ' ', self.body_html)
            # 解码HTML实体
            text = unescape(text)
            # 清理多余空白
            text = re.sub(r'\s+', ' ', text).strip()

            # 限制内容长度
            max_length = 6000  # 为LLM留出足够的token空间
            if len(text) > max_length:
                text = text[:max_length] + "..."

            logger.debug(f"上下文长度: {len(text)}")
            return text

        logger.warning("没有可用的HTML内容")
        return ""
    
    def _extract_information(self, question: str, retrieval_query: str) -> str:
        """
        Generic method to extract specific information using the retriever and LLM.
        Args:
            question (str): The question to ask the LLM for extraction.
            retrieval_query (str): The query to use for retrieving relevant context.
        Returns:
            str: The extracted information.
        """
        # 如果在降级模式，使用简单的文本解析
        if self.fallback_mode:
            return self._extract_information_fallback(question, retrieval_query)

        try:
            context = self._retrieve_context(retrieval_query)

            prompt = ChatPromptTemplate.from_template(
                template="""
                You are an expert in extracting specific information from job descriptions.
                Carefully read the job description context below and provide a clear and concise answer to the question.

                Context: {context}

                Question: {question}
                Answer:
                """
            )

            formatted_prompt = prompt.format(context=context, question=question)
            logger.debug(f"Formatted prompt for extraction: {formatted_prompt[:200]}...")  # Log the first 200 characters

            chain = prompt | self.llm | StrOutputParser()
            result = chain.invoke({"context": context, "question": question})
            extracted_info = result.strip()
            logger.debug(f"Extracted information: {extracted_info}")
            return extracted_info
        except Exception as e:
            logger.error(f"Error during information extraction: {e}")
            logger.warning("LLM提取失败，切换到降级模式")
            self.fallback_mode = True
            return self._extract_information_fallback(question, retrieval_query)

    def _extract_information_fallback(self, question: str, retrieval_query: str) -> str:
        """
        降级模式：使用简单的文本解析提取信息
        Args:
            question (str): 要提取的信息类型
            retrieval_query (str): 检索查询
        Returns:
            str: 提取的信息
        """
        if not hasattr(self, 'body_html') or not self.body_html:
            logger.error("没有可用的HTML内容进行降级解析")
            return "信息提取失败"

        # 清理HTML标签，获取纯文本
        import re
        from html import unescape

        # 移除HTML标签
        text = re.sub(r'<[^>]+>', ' ', self.body_html)
        # 解码HTML实体
        text = unescape(text)
        # 清理多余空白
        text = re.sub(r'\s+', ' ', text).strip()

        logger.debug(f"降级模式解析文本长度: {len(text)}")

        # 根据问题类型进行简单的文本匹配
        if "company" in question.lower() or "公司" in question:
            return self._extract_company_fallback(text)
        elif "role" in question.lower() or "title" in question.lower() or "职位" in question:
            return self._extract_role_fallback(text)
        elif "location" in question.lower() or "地点" in question:
            return self._extract_location_fallback(text)
        elif "description" in question.lower() or "描述" in question:
            return self._extract_description_fallback(text)
        else:
            return "无法识别的信息类型"
    
    def extract_job_description(self) -> str:
        """
        Extracts the detailed job description including responsibilities, requirements, and qualifications.
        Returns:
            str: The extracted job description with full details.
        """
        question = """Please extract and organize the job information in a clean, simple text format. DO NOT use any markdown formatting like **, *, or ###. Use plain text only.

        Structure the output as follows:

        [Company Name] is seeking a [Job Title] for [Location]. [Brief 1-2 sentence overview of the role and company context.]

        Job Responsibilities:
        • [List main duties and responsibilities using simple bullet points]

        Required Qualifications:
        • [List required skills, experience, and qualifications using simple bullet points]

        Preferred Qualifications:
        • [List preferred but not required qualifications using simple bullet points]

        Company Information:
        • [Brief company background and any mentioned benefits using simple bullet points]

        IMPORTANT: Use only plain text with simple bullet points (•). Do not use any bold (**), italic (*), or heading (###) markdown formatting. Keep the text clean, simple, and professional."""
        retrieval_query = "Job description responsibilities requirements qualifications"
        logger.debug("Starting comprehensive job description extraction.")
        return self._extract_information(question, retrieval_query)
    
    def extract_company_name(self) -> str:
        """
        Extracts the company name from the job description.
        Returns:
            str: The extracted company name.
        """
        question = "What is the company's name? Please provide only the main company name, not a description or explanation. For example, if the job is at 'Marco Polo Wuhan' which is part of 'Wharf Hotels', just return 'Wharf Hotels' or 'Marco Polo Hotels' - whichever is the main hiring company."
        retrieval_query = "Company name"
        logger.debug("Starting company name extraction.")
        result = self._extract_information(question, retrieval_query)

        # 后处理：如果结果包含解释性文字，尝试提取主要公司名称
        if result and len(result) > 50:  # 如果结果太长，可能包含解释
            import re
            # 尝试提取第一个提到的公司名称
            patterns = [
                r'([A-Z][A-Za-z\s&]+(?:Hotels?|Group|Company|Corp|Inc|Ltd|Limited))',
                r'([A-Z][A-Za-z\s&]{2,30})',  # 一般的公司名称模式
            ]

            for pattern in patterns:
                matches = re.findall(pattern, result)
                if matches:
                    # 返回第一个匹配的公司名称
                    company_name = matches[0].strip()
                    if len(company_name) < 50:  # 确保不是太长的描述
                        return company_name

        return result
    
    def extract_role(self) -> str:
        """
        Extracts the sought role/title from the job description.
        Returns:
            str: The extracted role/title.
        """
        question = "What is the role or title sought in this job description?"
        retrieval_query = "Job title"
        logger.debug("Starting role/title extraction.")
        return self._extract_information(question, retrieval_query)
    
    def extract_location(self) -> str:
        """
        Extracts the location from the job description.
        Returns:
            str: The extracted location.
        """
        question = "What is the location mentioned in this job description?"
        retrieval_query = "Location"
        logger.debug("Starting location extraction.")
        return self._extract_information(question, retrieval_query)
    
    def extract_recruiter_email(self) -> str:
        """
        Extracts the recruiter's email from the job description.
        Returns:
            str: The extracted recruiter's email.
        """
        question = "What is the recruiter's email address in this job description?"
        retrieval_query = "Recruiter email"
        logger.debug("Starting recruiter email extraction.")
        email = self._extract_information(question, retrieval_query)
        
        # Validate the extracted email using regex
        email_regex = r'[\w\.-]+@[\w\.-]+\.\w+'
        if re.match(email_regex, email):
            logger.debug("Valid recruiter's email.")
            return email
        else:
            logger.warning("Invalid or not found recruiter's email.")
            return ""

    def _extract_company_fallback(self, text: str) -> str:
        """降级模式：提取公司名称"""
        import re

        # 常见的公司名称模式，优先匹配包含公司后缀的名称
        patterns = [
            r'([A-Za-z][A-Za-z0-9\s&.,\-]*(?:Hotels?|Group|Company|Corp|Inc|Ltd|Limited|Technologies?|Systems?|Solutions?))',  # 带公司后缀
            r'<title[^>]*>.*?-\s*([A-Za-z][A-Za-z0-9\s&.,\-]{2,30}?)\s*</title>',  # 从标题提取，限制长度
            r'Company[:：]\s*([A-Za-z][A-Za-z0-9\s&.,\-]{2,30}?)(?:\s|$|,|\.|;)',
            r'(?:at|@|公司[:：]?)\s*([A-Za-z][A-Za-z0-9\s&.,\-]{2,30}?)(?:\s|$|,|\.|;)',
            r'([A-Za-z][A-Za-z0-9\s&.,\-]{2,30}?)\s*(?:is hiring|招聘|正在招聘)',
            r'Join\s+([A-Za-z][A-Za-z0-9\s&.,\-]{2,30}?)(?:\s|$|,|\.|;)',
            r'team at\s+([A-Za-z][A-Za-z0-9\s&.,\-]{2,30}?)(?:\s|$|,|\.|;)',
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
            if matches:
                company = matches[0].strip()
                # 过滤掉常见的无效词汇和过长的描述
                invalid_words = ['the', 'and', 'for', 'with', 'our', 'team', 'role', 'position', 'job', 'work', 'company', 'is', 'are']
                if len(company) > 2 and len(company) < 50 and company.lower() not in invalid_words:
                    # 进一步清理，移除描述性文字
                    company = re.sub(r'\s+is\s+.*$', '', company, flags=re.IGNORECASE)
                    company = re.sub(r'\s+are\s+.*$', '', company, flags=re.IGNORECASE)
                    return company.strip()

        return "未知公司"

    def _extract_role_fallback(self, text: str) -> str:
        """降级模式：提取职位名称"""
        import re

        # 常见的职位标题模式
        patterns = [
            r'<title[^>]*>([^<\-]+?)(?:\s*-.*)?</title>',  # 从标题提取，去掉公司部分
            r'<h1[^>]*>([^<]+)</h1>',  # 从h1标签提取
            r'(?:Job Title|Position|Role|职位[:：]?)\s*[:：]?\s*([A-Za-z][A-Za-z0-9\s\-/]{2,50}?)(?:\s|$|,|\.|;)',
            r'(?:We are looking for|Looking for|招聘)\s+(?:a|an)?\s*([A-Za-z][A-Za-z0-9\s\-/]{2,50}?)(?:\s|$|,|\.|;)',
            r'([A-Za-z][A-Za-z0-9\s\-/]{2,50}?)\s*(?:position|role|job|职位)(?:\s|$|,|\.|;)',
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
            if matches:
                role = matches[0].strip()
                # 清理常见的无效词汇
                invalid_words = ['the', 'and', 'for', 'with', 'our', 'team', 'company', 'at']
                if len(role) > 2 and role.lower() not in invalid_words:
                    return role

        return "未知职位"

    def _extract_location_fallback(self, text: str) -> str:
        """降级模式：提取工作地点"""
        import re

        # 常见的地点模式
        patterns = [
            r'Location[:：]?\s*([A-Za-z\u4e00-\u9fff][A-Za-z0-9\s,\-\u4e00-\u9fff]{2,50}?)(?:\s*</|$|,|\.|;)',
            r'(?:Based in|Located in|位于)\s*([A-Za-z\u4e00-\u9fff][A-Za-z0-9\s,\-\u4e00-\u9fff]{2,50}?)(?:\s|$|,|\.|;)',
            r'([A-Za-z\u4e00-\u9fff][A-Za-z0-9\s,\-\u4e00-\u9fff]{2,50}?)\s*(?:office|办公室|分公司)(?:\s|$|,|\.|;)',
            r'([A-Z][a-z]+,\s*[A-Z]{2})',  # 匹配 "Boston, MA" 格式
            r'([A-Z][a-z]+\s+[A-Z][a-z]+)',  # 匹配 "New York" 格式
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                location = matches[0].strip()
                # 过滤掉常见的无效词汇
                invalid_words = ['company', 'team', 'role', 'position', 'job', 'work', 'the', 'and']
                if len(location) > 2 and location.lower() not in invalid_words:
                    return location

        return "未知地点"

    def _extract_description_fallback(self, text: str) -> str:
        """降级模式：提取职位描述"""
        import re

        # 清理HTML标签
        clean_text = re.sub(r'<[^>]+>', ' ', text)
        clean_text = re.sub(r'\s+', ' ', clean_text).strip()

        # 尝试找到职位描述部分 - 更全面的模式
        desc_patterns = [
            # 英文模式
            r'(?:Job Description|Description|About this role|About the role|Role Overview|Position Summary)[:：]?\s*(.*?)(?:Requirements|Qualifications|Skills|Experience|What we|Benefits|Apply|$)',
            r'(?:Responsibilities|Key Responsibilities|Main Responsibilities|Your responsibilities)[:：]?\s*(.*?)(?:Requirements|Qualifications|Skills|Experience|What we|Benefits|Apply|$)',
            r'(?:What you.ll do|What you will do|Your role|In this role)[:：]?\s*(.*?)(?:Requirements|Qualifications|Skills|Experience|What we|Benefits|Apply|$)',
            # 中文模式
            r'(?:职位描述|工作描述|岗位职责|工作职责|职责描述|岗位要求)[:：]?\s*(.*?)(?:任职要求|技能要求|工作要求|福利待遇|申请|$)',
            r'(?:关于职位|职位介绍|岗位介绍)[:：]?\s*(.*?)(?:任职要求|技能要求|工作要求|福利待遇|申请|$)',
        ]

        best_description = ""
        max_length = 0

        for pattern in desc_patterns:
            matches = re.findall(pattern, clean_text, re.IGNORECASE | re.DOTALL)
            if matches:
                for match in matches:
                    description = match.strip()
                    if len(description) > max_length and len(description) > 50:
                        best_description = description
                        max_length = len(description)

        if best_description:
            # 清理描述，限制长度，并简化格式
            best_description = re.sub(r'\s+', ' ', best_description)
            # 移除过多的markdown格式
            best_description = re.sub(r'\*\*([^*]+)\*\*', r'\1', best_description)  # 移除粗体
            best_description = re.sub(r'\*([^*]+)\*', r'\1', best_description)      # 移除斜体
            best_description = re.sub(r'#+\s*', '', best_description)               # 移除标题符号
            return best_description[:1000] + "..." if len(best_description) > 1000 else best_description

        # 如果没有找到特定的描述部分，尝试提取主要内容
        # 查找包含关键词的段落
        keywords = ['responsibilities', 'requirements', 'qualifications', 'experience', 'skills',
                   'role', 'position', '职责', '要求', '经验', '技能', '岗位']

        sentences = clean_text.split('.')
        relevant_sentences = []

        for sentence in sentences:
            if any(keyword.lower() in sentence.lower() for keyword in keywords):
                relevant_sentences.append(sentence.strip())

        if relevant_sentences:
            description = '. '.join(relevant_sentences[:5])  # 取前5个相关句子
            return description[:1000] + "..." if len(description) > 1000 else description

        # 最后的降级：返回文本的前1000个字符
        if len(clean_text) > 100:
            return clean_text[:1000] + "..."

        return "职位描述信息不完整"

    def generate_cover_letter(self, job_info: dict, resume_text: str, user_name: str = "此致敬礼") -> str:
        """
        生成求职信内容
        Args:
            job_info: 职位信息字典，包含company, role, location, description等
            resume_text: 优化后的简历文本内容
        Returns:
            str: 生成的求职信HTML内容
        """
        try:
            company = job_info.get('company', '目标公司')
            role = job_info.get('role', '目标职位')
            location = job_info.get('location', '工作地点')
            job_description = job_info.get('description', '')

            # 检测简历语言
            def detect_language(text):
                """检测文本的主要语言"""
                if not text:
                    return 'english'

                # 计算中文字符比例
                chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
                total_chars = len(text.replace(' ', ''))

                if total_chars == 0:
                    return 'english'

                chinese_ratio = chinese_chars / total_chars
                return 'chinese' if chinese_ratio > 0.3 else 'english'

            resume_language = detect_language(resume_text)
            print(f"检测到的简历语言: {resume_language}")

            # 根据语言选择提示模板
            if resume_language == 'chinese':
                # 中文提示
                prompt = f"""
                请为以下职位生成一封个性化的求职信内容，要求深度结合职位要求和简历背景。

                职位信息：
                - 公司：{company}
                - 职位：{role}
                - 地点：{location}
                - 职位描述：{job_description[:2000]}

                简历信息：
                {resume_text[:3000]}

                **核心要求**：
                1. **内容长度**：主体求职信内容不超过350字（中文），简洁有力且有说服力
                2. **个性化程度**：深度分析职位要求，结合简历中的具体经验、技能、项目来撰写
                3. **双语对照**：生成中英文双语版本，中文在上，英文在下，用分隔线区分
                4. **专业性**：语言正式专业，严禁使用任何表情符号（包括✨、💼、🎯等），重点突出能力匹配
                5. **针对性**：必须体现对该具体公司文化、职位职责的深入理解

                **输出格式要求**：
                请只返回求职信的主体内容部分，不要包含问候语、结尾语、签名等，这些会由系统自动添加。

                **重要**：不要生成以下内容（系统会自动添加）：
                - 问候语（如"尊敬的...招聘团队，您好！"）
                - 结尾语（如"感谢您的时间和考虑"）
                - 签名部分
                - HTML标签（如```html）
                - 任何表情符号（如✨、💼、🎯、😊等）

                只需要返回纯文本的主体内容，包含：
                1. **中文求职信内容**（不超过350字）
                2. **分隔线**：用"---"表示
                3. **英文求职信内容**（相应简洁版本）

                **示例输出**：
                ```
                我对 {company} 的 {role} 职位充满热情。通过深入了解贵公司的业务发展和企业文化，我深深被其创新精神和专业态度所吸引。

                在我的职业生涯中，我积累了丰富的相关经验...（继续中文内容，不超过350字）

                ---

                I am excited about the opportunity to join {company} as a {role}. After thoroughly researching your company's business development and corporate culture, I am deeply impressed by your innovative spirit and professional approach.

                Throughout my career, I have accumulated extensive relevant experience...（继续英文内容，相应简洁版本）
                ```

                **重要提醒**：
                - 中英文内容都必须充实、有针对性，避免模板化表达
                - 深度挖掘简历中的具体经验和成就，与职位要求精准对接
                - 体现对公司和行业的深入研究和理解
                - 语言专业且有说服力，展现候选人的专业素养
                - 数据分析要基于实际的简历和职位匹配情况进行合理评估
                """
            else:
                # 英文提示
                prompt = f"""
                Please generate a personalized cover letter content for the following position, requiring deep integration of job requirements and resume background.

                Position Information:
                - Company: {company}
                - Position: {role}
                - Location: {location}
                - Job Description: {job_description[:2000]}

                Resume Information:
                {resume_text[:3000]}

                **Core Requirements**:
                1. **Content Length**: Main cover letter content should not exceed 350 words (English), concise and persuasive
                2. **Personalization**: Deep analysis of job requirements, combining specific experience, skills, and projects from resume
                3. **Bilingual Format**: Generate bilingual Chinese-English version, Chinese first, English second, separated by divider
                4. **Professionalism**: Formal professional language, strictly prohibit any emojis (including ✨, 💼, 🎯, etc.), focus on capability matching
                5. **Targeting**: Must demonstrate deep understanding of specific company culture and job responsibilities

                **Output Format Requirements**:
                Please only return the main body content of the cover letter, do not include greetings, closing remarks, signatures, etc., as these will be automatically added by the system.

                **Important**: Do not generate the following content (will be automatically added by system):
                - Greetings (such as "Dear ... Recruitment Team, Hello!")
                - Closing remarks (such as "Thank you for your time and consideration")
                - Signature section
                - HTML tags (such as ```html)
                - Any emojis (such as ✨, 💼, 🎯, 😊, etc.)

                Only return plain text main content, including:
                1. **Chinese cover letter content** (not exceeding 350 characters)
                2. **Divider**: Use "---" to indicate
                3. **English cover letter content** (corresponding concise version)

                **Example Output**:
                ```
                我对 {company} 的 {role} 职位充满热情。通过深入了解贵公司的业务发展和企业文化，我深深被其创新精神和专业态度所吸引。

                在我的职业生涯中，我积累了丰富的相关经验...（继续中文内容，不超过350字）

                ---

                I am excited about the opportunity to join {company} as a {role}. After thoroughly researching your company's business development and corporate culture, I am deeply impressed by your innovative spirit and professional approach.

                Throughout my career, I have accumulated extensive relevant experience...（继续英文内容，相应简洁版本）
                ```

                **Important Reminders**:
                - Both Chinese and English content must be substantial and targeted, avoiding template expressions
                - Deeply explore specific experiences and achievements from resume, precisely aligning with job requirements
                - Demonstrate deep research and understanding of company and industry
                - Professional and persuasive language, showcasing candidate's professional competence
                - Data analysis should be based on actual resume and job matching situations for reasonable assessment
                """

            if self.fallback_mode or not self.llm:
                return self._generate_cover_letter_fallback(job_info, resume_text, user_name)

            # 使用LLM生成求职信
            if hasattr(self.llm, '__call__'):
                # LoggerChatModel使用__call__方法
                result = self.llm([{"role": "user", "content": prompt}])
            elif hasattr(self.llm, 'invoke'):
                result = self.llm.invoke(prompt)
            elif hasattr(self.llm, 'predict'):
                result = self.llm.predict(prompt)
            else:
                # 如果是LoggerChatModel包装的，尝试访问内部模型
                if hasattr(self.llm, 'llm') and hasattr(self.llm.llm, 'invoke'):
                    result = self.llm.llm.invoke(prompt)
                else:
                    raise Exception("无法调用LLM模型")

            if hasattr(result, 'content'):
                content = result.content
            else:
                content = str(result)

            # 处理LLM生成的纯文本内容
            content = content.strip()

            # 移除可能的代码块标记
            if content.startswith('```html'):
                content = content[7:]
            elif content.startswith('```'):
                content = content[3:]
            if content.endswith('```'):
                content = content[:-3]
            content = content.strip()

            # 移除可能的HTML标签（如果LLM仍然生成了HTML）
            import re
            # 移除HTML标签，但保留内容
            content = re.sub(r'<[^>]+>', '', content)
            # 移除可能的✨符号
            content = content.replace('✨', '')
            content = content.strip()

            # 将纯文本内容包装成完整的HTML结构
            content = self._wrap_cover_letter_content(content, job_info, user_name)

            return content

        except Exception as e:
            logger.error(f"求职信生成失败: {str(e)}")
            return self._generate_cover_letter_fallback(job_info, resume_text, user_name)

    def _generate_cover_letter_fallback(self, job_info: dict, resume_text: str, user_name: str = "此致敬礼") -> str:
        """
        降级模式：生成基础求职信
        """
        company = job_info.get('company', '目标公司')
        role = job_info.get('role', '目标职位')
        location = job_info.get('location', '工作地点')

        # 分析简历关键词，生成更个性化的内容
        skills_keywords = self._extract_key_skills(resume_text)
        experience_years = self._estimate_experience_years(resume_text)

        # 生成技能文本
        skills_text = "、".join(skills_keywords[:5]) if skills_keywords else "相关技能"
        skills_text_en = ", ".join(skills_keywords[:5]) if skills_keywords else "relevant skills"

        # 生成纯文本内容，让包装函数处理HTML结构
        fallback_text = f"""我对 {company} 的 {role} 职位充满热情。通过深入了解贵公司的业务发展和企业文化，我深深被其创新精神和专业态度所吸引。

在我的职业生涯中，我积累了丰富的相关经验，在{skills_text}等核心技能方面具备扎实的专业能力。我始终关注行业发展趋势，持续学习新技术和方法论。

我相信，凭借我的专业背景和工作经验，能够为{company}的发展贡献自己的力量。我期待能够加入{company}这个优秀的团队，在{role}这个职位上发挥自己的专长，实现个人职业发展与公司发展的双赢。

---

I am genuinely excited about the {role} position at {company}. After thoroughly researching your company's business development and corporate culture, I am deeply impressed by your innovative spirit and professional approach.

Throughout my career, I have accumulated extensive relevant experience with solid professional capabilities in core skills including {skills_text_en}. I consistently follow industry trends and continuously learn new technologies and methodologies.

I believe that with my professional background and work experience, I can contribute to {company}'s development. I look forward to joining the excellent team at {company} and leveraging my expertise in the {role} position to achieve a win-win situation between personal career development and company growth."""

        # 移除可能的✨符号
        fallback_text = fallback_text.replace('✨', '')

        # 使用包装函数生成完整的HTML结构
        return self._wrap_cover_letter_content(fallback_text, job_info, user_name)

    def _extract_key_skills(self, resume_text: str) -> list:
        """从简历中提取关键技能"""
        # 常见技能关键词
        skill_keywords = [
            'Python', 'Java', 'JavaScript', 'React', 'Vue', 'Angular', 'Node.js',
            'SQL', 'MySQL', 'PostgreSQL', 'MongoDB', 'Redis',
            'AWS', 'Azure', 'Docker', 'Kubernetes', 'Git',
            '项目管理', '团队管理', '数据分析', '机器学习', '人工智能',
            '产品设计', '用户体验', 'UI设计', '市场营销', '销售',
            '财务分析', '风险管理', '人力资源', '招聘', '培训'
        ]

        found_skills = []
        resume_lower = resume_text.lower()

        for skill in skill_keywords:
            if skill.lower() in resume_lower:
                found_skills.append(skill)

        return found_skills[:5]  # 返回前5个技能

    def _estimate_experience_years(self, resume_text: str) -> int:
        """估算工作经验年数"""
        import re

        # 查找年份模式
        year_pattern = r'20\d{2}'
        years = re.findall(year_pattern, resume_text)

        if len(years) >= 2:
            years = [int(y) for y in years]
            return max(years) - min(years)

        # 查找经验描述
        experience_patterns = [
            r'(\d+)\s*年.*经验',
            r'(\d+)\s*years.*experience',
            r'经验.*(\d+)\s*年'
        ]

        for pattern in experience_patterns:
            match = re.search(pattern, resume_text, re.IGNORECASE)
            if match:
                return int(match.group(1))

        return 3  # 默认3年经验

    def _generate_personalized_content(self, company: str, role: str, skills: list, years: int) -> str:
        """生成个性化的主体内容"""
        # 根据技能和经验生成个性化内容
        skills_text = "、".join(skills[:5]) if skills else "相关技能"
        skills_text_en = ", ".join(skills[:5]) if skills else "relevant skills"

        if years <= 2:
            experience_desc = "虽然我是职场新人，但学习能力强，具备扎实的理论基础"
        elif years <= 5:
            experience_desc = f"凭借{years}年的实战经验，在相关领域积累了丰富的项目经验"
        else:
            experience_desc = f"拥有{years}年丰富经验，在行业内具备深厚的专业积淀"

        # 生成中英双语内容
        chinese_content = f"""
        <div class="chinese-content">
            <p>我对 <span class="highlight">{company}</span> 的 <span class="highlight">{role}</span> 职位充满热情。通过深入了解贵公司的业务发展和企业文化，我深深被其创新精神和专业态度所吸引。</p>

            <p>{experience_desc}，在{skills_text}等核心技能方面具备扎实的专业能力。我始终关注行业发展趋势，持续学习新技术和方法论，确保自己的专业技能与时俱进。</p>

            <p>在以往的工作经历中，我不仅注重技术能力的提升，更重视团队协作和项目管理能力的培养。我相信，凭借我的专业背景和工作经验，能够为{company}的发展贡献自己的力量，与团队一起创造更大的价值。</p>

            <p>我期待能够加入{company}这个优秀的团队，在{role}这个职位上发挥自己的专长，为公司的持续发展和创新贡献力量。同时，我也希望在这个平台上不断学习成长，实现个人职业发展与公司发展的双赢。</p>
        </div>
        """

        english_content = f"""
        <div class="separator">
            <hr style="margin: 30px 0; border: none; height: 2px; background: linear-gradient(90deg, #667eea, #764ba2);">
        </div>

        <div class="english-content">
            <p>I am genuinely excited about the <span class="highlight">{role}</span> position at <span class="highlight">{company}</span>. After thoroughly researching your company's business development and corporate culture, I am deeply impressed by your innovative spirit and professional approach.</p>

            <p>{experience_desc.replace('年', ' years').replace('虽然我是职场新人，但学习能力强，具备扎实的理论基础', 'Although I am new to the workforce, I have strong learning abilities and solid theoretical foundations').replace('凭借', 'With').replace('的实战经验，在相关领域积累了丰富的项目经验', ' of practical experience, I have accumulated rich project experience in related fields').replace('拥有', 'With').replace('丰富经验，在行业内具备深厚的专业积淀', ' of extensive experience, I have deep professional expertise in the industry')}, with solid professional capabilities in core skills including {skills_text_en}. I consistently follow industry trends and continuously learn new technologies and methodologies to ensure my professional skills remain current.</p>

            <p>Throughout my career, I have focused not only on enhancing technical capabilities but also on developing teamwork and project management skills. I believe that with my professional background and work experience, I can contribute to {company}'s development and create greater value together with the team.</p>

            <p>I look forward to joining the excellent team at {company} and leveraging my expertise in the {role} position to contribute to the company's continued development and innovation. I also hope to continuously learn and grow on this platform, achieving a win-win situation between personal career development and company growth.</p>
        </div>
        """

        return chinese_content + english_content

    def _calculate_skill_match(self, skills: list, role: str) -> int:
        """计算技能匹配度"""
        if not skills:
            return 75

        # 根据职位类型和技能数量计算匹配度
        role_lower = role.lower()
        tech_roles = ['开发', '工程师', '程序员', 'developer', 'engineer']

        base_score = 70
        if any(tech in role_lower for tech in tech_roles):
            base_score += len(skills) * 3
        else:
            base_score += len(skills) * 2

        return min(base_score, 95)

    def _calculate_experience_match(self, years: int, role: str) -> int:
        """计算经验相关性"""
        role_lower = role.lower()

        # 根据职位级别调整期望经验
        if any(word in role_lower for word in ['senior', '高级', '资深', 'lead', '主管']):
            expected_years = 5
        elif any(word in role_lower for word in ['junior', '初级', '助理']):
            expected_years = 1
        else:
            expected_years = 3

        # 计算匹配度
        if years >= expected_years:
            return min(85 + (years - expected_years) * 2, 95)
        else:
            return max(70 - (expected_years - years) * 5, 60)

    def _calculate_learning_potential(self, skills: list) -> int:
        """计算学习潜力"""
        # 基于技能多样性计算学习潜力
        base_score = 85
        if len(skills) >= 4:
            base_score = 95
        elif len(skills) >= 2:
            base_score = 90

        return base_score

    def _wrap_cover_letter_content(self, content: str, job_info: dict, user_name: str = "此致敬礼") -> str:
        """
        将生成的内容包装成完整的HTML结构
        """
        company = job_info.get('company', '目标公司')

        # 移除可能的✨符号
        content = content.replace('✨', '')

        # 处理纯文本内容，分割中英文
        if '---' in content:
            parts = content.split('---', 1)
            chinese_content = parts[0].strip()
            english_content = parts[1].strip() if len(parts) > 1 else ""
        else:
            chinese_content = content
            english_content = ""

        # 将纯文本转换为HTML段落
        def text_to_html_paragraphs(text):
            if not text:
                return ""
            paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
            return '\n'.join([f'<p>{p}</p>' for p in paragraphs])

        chinese_html = text_to_html_paragraphs(chinese_content)
        english_html = text_to_html_paragraphs(english_content)

        # 构建完整的HTML结构
        main_content = f"""
        <div class="chinese-content">
            {chinese_html}
        </div>
        """

        if english_html:
            main_content += f"""
            <div class="separator">
                <hr style="margin: 30px 0; border: none; height: 2px; background: linear-gradient(90deg, #667eea, #764ba2);">
            </div>
            <div class="english-content">
                {english_html}
            </div>
            """

        return f"""
        <div class="greeting">
            尊敬的 {company} 招聘团队，您好！
        </div>

        <div class="main-content">
            {main_content}
        </div>

        <div class="closing">
            感谢您的时间和考虑！期待与您进一步交流的机会。
        </div>

        <div class="signature">
            <div class="signature-name">{user_name}</div>
            <div class="signature-date">{self._get_current_date()}</div>
        </div>
        """

    def _get_current_date(self) -> str:
        """获取当前日期"""
        from datetime import datetime
        return datetime.now().strftime("%Y年%m月%d日")

    def parse_resume_content(self, resume_text: str) -> dict:
        """
        解析简历内容，提取关键信息
        """
        try:
            prompt = f"""
            请分析以下简历内容，提取关键信息并以JSON格式返回：

            简历内容：
            {resume_text}

            请提取以下信息：
            1. 个人信息 (personal_info):
               - 姓名 (name)
               - 邮箱 (email)
               - 电话 (phone)
               - 地址 (address)
               - LinkedIn (linkedin)
               - GitHub (github)
            2. 教育背景 (education) - 数组格式，每项包含：
               - 学校名称 (school)
               - 专业 (major)
               - 学位 (degree)
               - 毕业时间 (graduation_date)
               - GPA (gpa)
            3. 工作经验 (work_experience) - 数组格式，每项包含：
               - 公司名称 (company)
               - 职位 (position)
               - 工作时间 (duration)
               - 工作描述 (description) - 详细的工作职责和成就
               - 主要成就 (achievements)
            4. 技能 (skills) - 对象格式，分类：
               - 编程语言 (programming_languages) - 数组
               - 技术框架 (frameworks) - 数组
               - 工具软件 (tools) - 数组
               - 软技能 (soft_skills) - 数组
            5. 项目经验 (projects) - 数组格式，每项包含：
               - 项目名称 (name)
               - 项目描述 (description)
               - 使用技术 (technologies) - 数组
               - 项目时间 (duration)
            6. 证书资质 (certifications) - 数组格式，每项包含：
               - 证书名称 (name)
               - 证书描述 (description)
            7. 语言能力 (languages) - 数组格式，每项包含：
               - 语言 (language)
               - 熟练程度 (proficiency)

            重要提示：
            - 请仔细分析简历内容，不要遗漏任何重要信息
            - 工作描述应该详细提取，包括具体的职责、成就和贡献
            - 如果某些信息无法提取，请使用null或空数组，但不要使用"N/A"字符串
            - 确保返回有效的JSON格式，不要包含任何额外的文本或解释
            - 技能部分应该尽可能详细地分类提取

            请只返回JSON数据，不要包含任何其他文本。
            """

            # 使用正确的调用方法
            if hasattr(self.llm, '__call__'):
                # LoggerChatModel使用__call__方法
                response = self.llm([{"role": "user", "content": prompt}])
            elif hasattr(self.llm, 'invoke'):
                response = self.llm.invoke(prompt)
            elif hasattr(self.llm, 'predict'):
                response = self.llm.predict(prompt)
            else:
                # 如果是LoggerChatModel包装的，尝试访问内部模型
                if hasattr(self.llm, 'llm') and hasattr(self.llm.llm, 'invoke'):
                    response = self.llm.llm.invoke(prompt)
                else:
                    raise Exception("无法调用LLM模型")

            # 尝试解析JSON响应
            import json
            try:
                # 提取JSON部分
                content = response.content if hasattr(response, 'content') else str(response)
                print(f"LLM响应内容: {content[:500]}...")  # 调试信息

                # 查找JSON块
                import re
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    parsed_data = json.loads(json_str)
                    print(f"解析成功的简历数据: {parsed_data}")  # 调试信息

                    # 清理数据中的N/A值
                    cleaned_data = self._clean_resume_data(parsed_data)
                    return cleaned_data
                else:
                    print("未找到JSON块，返回空结构")  # 调试信息
                    # 如果没有找到JSON，返回基本结构
                    return self._get_empty_resume_structure()
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")  # 调试信息
                # JSON解析失败，返回基本结构
                return self._get_empty_resume_structure()

        except Exception as e:
            print(f"简历内容解析错误: {e}")
            return self._get_empty_resume_structure()

    def _clean_resume_data(self, data: dict) -> dict:
        """清理简历数据，移除N/A值和空值"""
        def clean_value(value):
            if isinstance(value, str):
                # 移除N/A、空字符串等无效值
                if value.strip().upper() in ['N/A', 'NA', 'NULL', 'NONE', '']:
                    return None
                return value.strip()
            elif isinstance(value, list):
                # 清理列表中的每个元素
                cleaned_list = []
                for item in value:
                    cleaned_item = clean_value(item)
                    if cleaned_item is not None:
                        cleaned_list.append(cleaned_item)
                return cleaned_list
            elif isinstance(value, dict):
                # 递归清理字典
                cleaned_dict = {}
                for k, v in value.items():
                    cleaned_v = clean_value(v)
                    if cleaned_v is not None:
                        cleaned_dict[k] = cleaned_v
                return cleaned_dict
            else:
                return value

        return clean_value(data)

    def _get_empty_resume_structure(self) -> dict:
        """返回空的简历数据结构"""
        return {
            "personal_info": {
                "name": None,
                "email": None,
                "phone": None,
                "address": None,
                "linkedin": None,
                "github": None
            },
            "education": [],
            "work_experience": [],
            "skills": {
                "programming_languages": [],
                "frameworks": [],
                "tools": [],
                "soft_skills": []
            },
            "projects": [],
            "certifications": [],
            "languages": []
        }

