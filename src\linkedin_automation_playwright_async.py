"""
LinkedIn自动化 - Playwright异步版本
支持在FastAPI等异步环境中使用
"""

import asyncio
import os
import random
import time
from typing import Optional, Dict, Any, List
from pathlib import Path

from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext
from loguru import logger

# 暂时移除这些导入，使用简单的配置加载
# from src.utils import ConfigValidator
# from src.job import Job
import yaml


class LinkedInAutomationPlaywrightAsync:
    """LinkedIn自动化 - Playwright异步版本"""
    
    def __init__(self, config_path: str = None):
        """初始化异步Playwright自动化"""
        self.config_path = config_path or "linkedin_config.yaml"
        self.config = self._load_config(config_path)
        
        # 异步Playwright对象
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
        # 配置参数
        self.headless = True
        self.user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        self.proxy = None
        
        logger.info("异步Playwright LinkedIn自动化初始化完成")

    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        if config_path and Path(config_path).exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)

        # 默认配置
        return {
            'linkedin': {
                'email': '',
                'password': '',
                'search_keywords': ['python developer', 'software engineer'],
                'location': 'United States',
                'experience_level': ['Entry level', 'Associate', 'Mid-Senior level'],
                'job_type': ['Full-time', 'Part-time', 'Contract'],
                'remote_work': ['On-site', 'Remote', 'Hybrid'],
                'max_applications_per_day': 50,
                'delay_between_applications': [30, 60],  # 秒
                'auto_answer_questions': True,
                'default_answers': {
                    'years_experience': '3',
                    'willing_to_relocate': 'Yes',
                    'authorized_to_work': 'Yes',
                    'require_sponsorship': 'No'
                }
            },
            'playwright': {
                'headless': False,
                'timeout': 30000,  # 毫秒
                'window_size': [1200, 800]
            }
        }
    
    async def setup_driver(self, headless: bool = True) -> Page:
        """设置异步Playwright浏览器"""
        try:
            await self.close()  # 确保关闭之前的实例

            self.headless = headless
            logger.info(f"设置异步Playwright浏览器，无头模式: {headless}")

            # 启动Playwright
            self.playwright = await async_playwright().start()
            logger.info("Playwright 启动成功")

            # 启动浏览器 - 使用最简单的配置
            try:
                self.browser = await self.playwright.chromium.launch(headless=headless)
                logger.info("浏览器启动成功")
            except Exception as browser_error:
                logger.error(f"浏览器启动失败: {browser_error}")
                # 尝试不带任何参数启动
                self.browser = await self.playwright.chromium.launch()
                logger.info("浏览器启动成功（无参数模式）")

            # 创建上下文 - 使用最简单的配置
            try:
                self.context = await self.browser.new_context()
                logger.info("上下文创建成功")
            except Exception as context_error:
                logger.error(f"上下文创建失败: {context_error}")
                raise

            # 创建页面
            try:
                self.page = await self.context.new_page()
                logger.info("页面创建成功")
            except Exception as page_error:
                logger.error(f"页面创建失败: {page_error}")
                raise

            # 设置超时
            try:
                self.page.set_default_timeout(30000)  # 30秒
                logger.info("超时设置成功")
            except Exception as timeout_error:
                logger.warning(f"超时设置失败: {timeout_error}")

            logger.info("异步Playwright浏览器设置完成")
            return self.page

        except Exception as e:
            logger.error(f"设置异步Playwright浏览器失败: {e}")
            await self.close()
            raise RuntimeError(f"设置异步Playwright浏览器失败: {e}")
    
    async def close(self):
        """关闭异步Playwright浏览器"""
        try:
            if self.page:
                await self.page.close()
                self.page = None
            
            if self.context:
                await self.context.close()
                self.context = None
            
            if self.browser:
                await self.browser.close()
                self.browser = None
            
            if self.playwright:
                await self.playwright.stop()
                self.playwright = None
            
            logger.info("异步Playwright浏览器已关闭")
            
        except Exception as e:
            logger.warning(f"关闭异步Playwright浏览器时出错: {e}")
    
    async def login(self, email: str = None, password: str = None) -> Dict[str, Any]:
        """异步登录LinkedIn"""
        try:
            if not self.page:
                raise RuntimeError("浏览器未初始化，请先调用setup_driver")

            # 从配置文件获取邮箱密码（如果未提供）
            if not email:
                email = self.config['linkedin']['email']
            if not password:
                password = self.config['linkedin']['password']

            if not email or not password:
                logger.error("LinkedIn邮箱或密码未配置")
                return {"success": False, "status": "邮箱或密码未配置", "requires_action": False}

            logger.info(f"开始异步登录LinkedIn... 使用邮箱: {email[:3]}****{email[-10:]}")

            # 访问LinkedIn登录页面
            try:
                logger.debug("正在访问LinkedIn登录页面...")
                await self.page.goto("https://www.linkedin.com/login", wait_until="networkidle")
                logger.debug(f"当前URL: {self.page.url}")
            except Exception as e:
                logger.error(f"无法访问LinkedIn登录页面: {str(e)}")
                return {"success": False, "status": "无法访问LinkedIn登录页面，请检查网络连接或代理设置", "requires_action": False}

            # 等待登录页面加载
            try:
                logger.debug("等待登录页面元素加载...")
                await self.page.wait_for_selector("#username", state="visible", timeout=15000)
                logger.debug("登录页面元素加载成功")
            except Exception as e:
                logger.error(f"登录页面加载超时或异常: {str(e)}")
                return {"success": False, "status": "登录页面加载超时，请检查网络连接速度", "requires_action": False}

            # 输入邮箱
            try:
                logger.debug("正在输入邮箱...")
                await self.page.click("#username")
                await self.page.fill("#username", email)
                logger.info("邮箱输入完成")
            except Exception as e:
                logger.error(f"邮箱输入失败: {str(e)}")
                return {"success": False, "status": "邮箱输入失败，请检查邮箱格式是否正确", "requires_action": False}

            # 输入密码
            try:
                logger.debug("正在输入密码...")
                await self.page.wait_for_selector("#password", state="visible", timeout=5000)
                await self.page.focus("#password")  # 确保聚焦
                await self.page.fill("#password", password)  # fill会自动清空再输入
                logger.info("密码输入完成")
            except Exception as e:
                logger.error(f"密码输入失败: {str(e)}")
                return {"success": False, "status": "密码输入失败，请检查密码格式是否正确", "requires_action": False}

            # 点击登录按钮
            try:
                logger.debug("正在点击登录按钮...")
                await self.page.click("button[type='submit']")
                logger.info("点击登录按钮完成")
            except Exception as e:
                logger.error(f"登录按钮点击失败: {str(e)}")
                return {"success": False, "status": "登录按钮点击失败，请稍后重试", "requires_action": False}

            # 等待登录完成，增加等待时间以确保页面加载
            logger.debug("等待登录处理...")
            await asyncio.sleep(8)

            # 检查是否需要验证码或其他验证
            if "challenge" in self.page.url or "checkpoint/challenge" in self.page.url:
                logger.warning("检测到验证码验证")
                return {"success": False, "status": "需要完成验证码验证，请在浏览器中完成验证后重试", "requires_action": True}

            # 检查是否需要二次验证（checkpoint）
            if "checkpoint" in self.page.url:
                logger.warning("检测到二次验证，等待用户完成验证...")
                max_wait = 120  # 最长等待秒数
                poll_interval = 2  # 检查间隔
                waited = 0
                while waited < max_wait:
                    try:
                        current_url = self.page.url
                        logger.debug(f"二次验证等待中，当前URL: {current_url}")
                        if ("feed" in current_url or "linkedin.com/feed" in current_url or
                            await self.page.query_selector(".global-nav") or await self.page.query_selector(".feed-identity-module")):
                            logger.info("二次验证后检测到已登录，进入首页")
                            self.is_logged_in = True
                            return {"success": True, "status": "登录成功", "requires_action": False}
                    except Exception as e:
                        logger.warning(f"等待二次验证时页面异常: {str(e)}")
                    await asyncio.sleep(poll_interval)
                    waited += poll_interval
                logger.error("二次验证超时，未检测到登录成功")
                return {"success": False, "status": "二次验证超时，请手动检查浏览器中的登录状态", "requires_action": True}

            # 检查是否登录成功 - 等待页面跳转和元素加载
            try:
                logger.debug("检查登录状态...")
                max_wait = 30  # 最长等待秒数
                poll_interval = 2  # 检查间隔
                waited = 0

                while waited < max_wait:
                    try:
                        url = self.page.url
                        logger.debug(f"检查登录状态，当前URL: {url}")

                        # 检查多个登录成功的指标
                        login_indicators = [
                            "feed" in url,
                            "mynetwork" in url,
                            "linkedin.com/feed" in url,
                            await self.page.query_selector(".global-nav") is not None,
                            await self.page.query_selector(".feed-identity-module") is not None,
                            await self.page.query_selector("[data-control-name='nav.settings_and_privacy']") is not None,
                            await self.page.query_selector(".nav-item__profile-member-photo") is not None
                        ]

                        # 如果任何一个指标为真，则认为已登录
                        if any(login_indicators):
                            logger.info("检测到已登录，进入主页")
                            self.is_logged_in = True
                            return {"success": True, "status": "登录成功", "requires_action": False}

                        # 检测是否仍在登录页面
                        if "login" in url or "challenge" in url:
                            logger.debug("仍在登录或验证页面，继续等待...")
                        else:
                            # 如果不在登录页面但也没有检测到登录指标，可能需要更多时间
                            logger.debug("页面已跳转但未检测到登录指标，继续等待...")

                    except Exception as e:
                        logger.warning(f"检测主页元素时页面异常: {str(e)}")

                    await asyncio.sleep(poll_interval)
                    waited += poll_interval

                logger.error("未检测到登录成功")
                logger.debug("当前页面HTML片段: " + await self.page.content()[:1000])
                return {"success": False, "status": "登录验证超时，请手动检查浏览器中的登录状态", "requires_action": True}

            except Exception as e:
                logger.error(f"登录验证失败: {str(e)}")
                return {"success": False, "status": "登录验证失败，请稍后重试", "requires_action": False}

        except Exception as e:
            logger.error(f"登录过程发生未知错误: {str(e)}")
            return {"success": False, "status": "登录过程发生未知错误，请稍后重试", "requires_action": False}

    async def verify_login_status(self) -> Dict:
        """手动验证登录状态

        Returns:
            包含验证结果的字典
        """
        try:
            logger.info("开始验证登录状态...")

            # 检查当前URL
            current_url = self.page.url
            logger.debug(f"当前URL: {current_url}")

            # 如果在登录页面，说明未登录
            if "login" in current_url:
                logger.info("当前在登录页面，未登录")
                self.is_logged_in = False
                return {"success": False, "status": "未登录", "requires_action": False}

            # 检查是否需要验证
            if "challenge" in current_url or "checkpoint" in current_url:
                logger.warning("检测到需要验证")
                return {"success": False, "status": "需要完成验证，请在浏览器中完成验证", "requires_action": True}

            # 检查登录指标（增强版）
            try:
                # URL检查（扩展更多指标）
                url_indicators = ["feed", "jobs", "/in/", "mynetwork", "messaging", "notifications", "search/results"]
                url_logged_in = any(indicator in current_url for indicator in url_indicators)
                
                # 页面元素检查（扩展更多选择器）
                element_checks = [
                    await self.page.query_selector(".global-nav") is not None,
                    await self.page.query_selector(".feed-identity-module") is not None,
                    await self.page.query_selector("[data-control-name='nav.settings_and_privacy']") is not None,
                    await self.page.query_selector(".nav-item__profile-member-photo") is not None,
                    await self.page.query_selector(".global-nav__me") is not None,
                    await self.page.query_selector(".global-nav__primary-link--me") is not None,
                    await self.page.query_selector("button[aria-label*='我']") is not None,
                    await self.page.query_selector("button[aria-label*='Me']") is not None,
                    await self.page.query_selector(".global-nav__nav") is not None,
                    await self.page.query_selector(".feed-identity") is not None
                ]
                
                login_indicators = [url_logged_in] + element_checks

                if any(login_indicators):
                    logger.info("检测到已登录")
                    self.is_logged_in = True
                    return {"success": True, "status": "已登录", "requires_action": False}
                else:
                    # 额外检查：页面内容检测
                    try:
                        page_content = await self.page.content()
                        content_indicators = ["global-nav", "feed-identity", "nav-item__profile", "linkedin.com/feed"]
                        content_logged_in = any(indicator in page_content for indicator in content_indicators)
                        
                        if content_logged_in:
                            logger.info("基于页面内容检测到已登录")
                            self.is_logged_in = True
                            return {"success": True, "status": "已登录", "requires_action": False}
                    except Exception as e:
                        logger.warning(f"页面内容检测时出错: {str(e)}")
                    
                    logger.warning("未检测到登录指标")
                    self.is_logged_in = False
                    return {"success": False, "status": "登录状态不明确，请重新登录", "requires_action": False}

            except Exception as e:
                logger.error(f"检查登录指标时出错: {str(e)}")
                return {"success": False, "status": "验证登录状态时出错", "requires_action": False}

        except Exception as e:
            logger.error(f"验证登录状态失败: {str(e)}")
            return {"success": False, "status": "验证失败", "requires_action": False}

    async def search_jobs(self, keywords: str = None, location: str = None, easy_apply_only: bool = True) -> List[Dict]:
        """异步搜索工作"""
        try:
            if not self.page:
                raise RuntimeError("浏览器未初始化，请先调用setup_driver")
            
            # 使用默认值如果参数为空
            if not keywords:
                keywords = self.config['linkedin']['search_keywords'][0]
            if not location:
                location = self.config['linkedin']['location']

            logger.info(f"开始异步搜索职位: {keywords} in {location}")

            # 构建搜索URL
            search_url = f"https://www.linkedin.com/jobs/search/?keywords={keywords}"
            if location:
                search_url += f"&location={location}"
            if easy_apply_only:
                search_url += "&f_AL=true"
            
            logger.debug(f"导航到搜索页面: {search_url}")

            # 访问搜索页面
            await self.page.goto(search_url, wait_until="networkidle")
            await asyncio.sleep(5)  # 增加等待时间

            # 滚动页面以加载更多职位
            await self._scroll_to_load_jobs_async()

            # 保存页面HTML快照
            from pathlib import Path
            log_dir = Path(__file__).parent.parent / "log"
            log_dir.mkdir(parents=True, exist_ok=True)
            html_file_path = log_dir / "linkedin_jobs_playwright_async.html"
            page_content = await self.page.content()
            with open(html_file_path, "w", encoding="utf-8") as f:
                f.write(page_content)
            logger.info(f"页面HTML快照已保存到: {html_file_path}")

            # 使用LLM解析职位信息
            jobs = await self._parse_jobs_with_llm_async(page_content)

            if not jobs:
                logger.warning("LLM解析未找到职位，尝试传统方法")
                # 回退到传统解析方法
                jobs = await self._extract_jobs_traditional_async()

            logger.info(f"异步搜索完成，找到 {len(jobs)} 个职位")
            return jobs
            
        except Exception as e:
            logger.error(f"异步搜索工作时出错: {e}")
            return []

    async def _scroll_to_load_jobs_async(self):
        """异步滚动页面以加载更多职位"""
        try:
            logger.info("开始滚动页面加载职位...")
            last_height = await self.page.evaluate("document.body.scrollHeight")
            scroll_attempts = 0
            max_scrolls = 5

            while scroll_attempts < max_scrolls:
                # 滚动到页面底部
                await self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                await asyncio.sleep(2)

                # 计算新的滚动高度并与上次的滚动高度进行比较
                new_height = await self.page.evaluate("document.body.scrollHeight")
                if new_height == last_height:
                    break
                last_height = new_height
                scroll_attempts += 1

            logger.info(f"滚动完成，共滚动 {scroll_attempts} 次")
        except Exception as e:
            logger.warning(f"滚动页面时出错: {str(e)}")

    async def _parse_jobs_with_llm_async(self, page_source: str) -> List[Dict]:
        """异步使用LLM解析职位信息"""
        try:
            logger.info("开始使用LLM解析职位信息...")

            # 获取API密钥
            api_key = self._get_api_key()
            if not api_key:
                logger.error("未找到API密钥，无法使用LLM解析")
                return []

            # 导入LLM相关模块
            from langchain_google_genai import ChatGoogleGenerativeAI
            from langchain_core.prompts import ChatPromptTemplate
            from langchain_core.output_parsers import StrOutputParser
            import json

            # 初始化LLM
            llm = ChatGoogleGenerativeAI(
                model="gemini-2.5-flash-preview-05-20",
                google_api_key=api_key,
                temperature=1.0,  # 使用用户偏好的温度设置
                request_timeout=60.0,
                max_retries=3,
                transport="rest"
            )

            # 创建解析提示
            prompt_template = ChatPromptTemplate.from_template("""
你是一个专业的LinkedIn职位信息解析专家。请从以下HTML页面源码中提取所有职位信息。

请仔细分析HTML内容，找出所有职位卡片，并提取以下信息：
1. 职位标题 (title)
2. 公司名称 (company)
3. 职位地点 (location)
4. 职位链接 (url) - 完整的LinkedIn职位链接
5. 是否支持Easy Apply (is_easy_apply) - true/false

请以JSON数组格式返回结果，每个职位一个对象。如果某个字段无法找到，请使用合理的默认值。

HTML内容：
{html_content}

请只返回JSON数组，不要包含任何其他文本或解释。格式如下：
[
  {{
    "title": "职位标题",
    "company": "公司名称",
    "location": "职位地点",
    "url": "https://www.linkedin.com/jobs/view/职位ID",
    "is_easy_apply": true,
    "job_id": "职位ID"
  }}
]
""")

            # 截取HTML内容的关键部分以避免token限制
            truncated_html = self._truncate_html_for_llm(page_source)

            # 创建链式处理
            chain = prompt_template | llm | StrOutputParser()

            # 调用LLM
            logger.info("正在调用LLM解析职位信息...")
            response = await chain.ainvoke({"html_content": truncated_html})

            # 解析JSON响应
            try:
                # 清理响应文本，移除可能的markdown标记
                clean_response = response.strip()
                if clean_response.startswith("```json"):
                    clean_response = clean_response[7:]
                if clean_response.endswith("```"):
                    clean_response = clean_response[:-3]
                clean_response = clean_response.strip()

                jobs_data = json.loads(clean_response)

                # 验证和清理数据
                validated_jobs = []
                for job in jobs_data:
                    if isinstance(job, dict) and job.get('title') and job.get('company'):
                        # 确保job_id存在
                        if not job.get('job_id'):
                            job['job_id'] = self._extract_job_id(job.get('url', '')) or str(hash(job.get('title', '') + job.get('company', '')))

                        # 确保URL是完整的LinkedIn链接
                        if job.get('url') and not job['url'].startswith('http'):
                            if job['url'].startswith('/'):
                                job['url'] = 'https://www.linkedin.com' + job['url']

                        validated_jobs.append(job)

                logger.info(f"LLM成功解析出 {len(validated_jobs)} 个职位")
                return validated_jobs

            except json.JSONDecodeError as e:
                logger.error(f"LLM响应JSON解析失败: {str(e)}")
                logger.debug(f"LLM原始响应: {response[:500]}...")
                return []

        except Exception as e:
            logger.error(f"LLM解析职位信息失败: {str(e)}")
            return []

    def _get_api_key(self) -> str:
        """获取API密钥"""
        try:
            # 尝试从多个来源获取API密钥
            import os
            from pathlib import Path
            import yaml

            # 1. 从环境变量获取
            api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
            if api_key:
                return api_key

            # 2. 从secrets.yaml文件获取
            secrets_file = Path(__file__).parent.parent / "secrets.yaml"
            if secrets_file.exists():
                with open(secrets_file, 'r', encoding='utf-8') as f:
                    secrets = yaml.safe_load(f)
                    api_key = secrets.get('gemini_api_key') or secrets.get('google_api_key') or secrets.get('llm_api_key')
                    if api_key:
                        return api_key

            # 3. 从data_folder/secrets.yaml获取
            data_secrets_file = Path(__file__).parent.parent / "data_folder" / "secrets.yaml"
            if data_secrets_file.exists():
                with open(data_secrets_file, 'r', encoding='utf-8') as f:
                    secrets = yaml.safe_load(f)
                    api_key = secrets.get('gemini_api_key') or secrets.get('google_api_key') or secrets.get('llm_api_key')
                    if api_key:
                        return api_key

            logger.warning("未找到API密钥")
            return None

        except Exception as e:
            logger.error(f"获取API密钥时出错: {str(e)}")
            return None

    def _truncate_html_for_llm(self, html_content: str, max_chars: int = 500000) -> str:
        """截取HTML内容以适应LLM token限制"""
        if len(html_content) <= max_chars:
            return html_content

        # 尝试找到职位相关的部分
        job_keywords = ['job', 'position', 'career', 'work', 'employment', 'base-search-card', 'job-card']

        # 查找包含职位关键词的部分
        best_start = 0
        best_score = 0

        for i in range(0, len(html_content) - max_chars, 1000):
            chunk = html_content[i:i + max_chars].lower()
            score = sum(chunk.count(keyword) for keyword in job_keywords)
            if score > best_score:
                best_score = score
                best_start = i

        truncated = html_content[best_start:best_start + max_chars]
        logger.info(f"HTML内容已截取: {len(html_content)} -> {len(truncated)} 字符")
        return truncated

    async def _extract_jobs_traditional_async(self) -> List[Dict]:
        """异步传统方法提取职位信息（作为LLM解析的备用方案）"""
        try:
            logger.info("使用传统方法提取职位信息...")

            # 尝试多种选择器
            selectors = [
                ".base-search-card",
                ".job-card-container",
                ".jobs-search-results__list-item",
                "[data-job-id]",
                ".job-card-list__item"
            ]

            job_cards = []
            for selector in selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    if elements:
                        job_cards = elements
                        logger.info(f"使用选择器 {selector} 找到 {len(job_cards)} 个职位卡片")
                        break
                except Exception:
                    continue

            if not job_cards:
                logger.warning("未找到任何职位卡片")
                return []

            jobs = []
            for card in job_cards[:20]:  # 限制前20个职位
                try:
                    job_info = await self._extract_job_info_async(card)
                    if job_info:
                        jobs.append(job_info)
                except Exception as e:
                    logger.warning(f"提取职位信息失败: {str(e)}")
                    continue

            logger.info(f"传统方法成功提取 {len(jobs)} 个职位")
            return jobs

        except Exception as e:
            logger.error(f"传统方法提取职位失败: {str(e)}")
            return []

    async def _extract_job_info_async(self, job_card) -> Optional[Dict]:
        """异步提取职位信息"""
        try:
            # 职位标题
            title_selectors = [
                ".job-card-list__title",
                ".job-card-list__title a",
                ".job-card-container__link",
                "h3 a",
                "a[data-control-name='job_card_title']",
                ".base-search-card__title a"
            ]

            title_element = None
            title = "未知职位"
            job_url = None

            for selector in title_selectors:
                title_element = await job_card.query_selector(selector)
                if title_element:
                    title = (await title_element.text_content()).strip()
                    job_url = await title_element.get_attribute('href')
                    break

            if job_url and not job_url.startswith('http'):
                job_url = f"https://www.linkedin.com{job_url}"

            # 公司名称
            company_selectors = [
                ".job-card-container__company-name",
                ".job-card-list__company-name",
                ".base-search-card__subtitle",
                "a[data-control-name='job_card_company_link']",
                ".job-card-container__primary-description"
            ]

            company = "未知公司"
            for selector in company_selectors:
                company_element = await job_card.query_selector(selector)
                if company_element:
                    company = (await company_element.text_content()).strip()
                    break

            # 地点
            location_selectors = [
                ".job-card-container__metadata-item",
                ".job-card-list__location",
                ".job-search-card__location",
                ".base-search-card__metadata .job-search-card__location"
            ]

            location = "未知地点"
            for selector in location_selectors:
                location_element = await job_card.query_selector(selector)
                if location_element:
                    location_text = (await location_element.text_content()).strip()
                    # 过滤掉时间信息，只保留地点
                    if location_text and not any(time_word in location_text.lower() for time_word in ['ago', 'day', 'week', 'month', 'hour']):
                        location = location_text
                        break

            # Easy Apply检测
            is_easy_apply = False
            easy_apply_selectors = [
                ".job-card-container__apply-method",
                ".job-card-list__easy-apply-label",
                "button:has-text('Easy Apply')",
                "button:has-text('轻松申请')",
                "[data-control-name='job_card_easy_apply']"
            ]

            for selector in easy_apply_selectors:
                try:
                    easy_apply_element = await job_card.query_selector(selector)
                    if easy_apply_element:
                        element_text = await easy_apply_element.text_content()
                        if element_text and ("Easy Apply" in element_text or "轻松申请" in element_text):
                            is_easy_apply = True
                            break
                except Exception:
                    continue

            # 如果还没找到，检查整个卡片的HTML内容
            if not is_easy_apply:
                try:
                    card_html = await job_card.inner_html()
                    if "Easy Apply" in card_html or "轻松申请" in card_html:
                        is_easy_apply = True
                except Exception:
                    pass

            # 生成job_id
            job_id = self._extract_job_id(job_url) if job_url else str(hash(title + company + location))

            return {
                'title': title,
                'company': company,
                'location': location,
                'url': job_url,
                'is_easy_apply': is_easy_apply,
                'job_id': job_id
            }

        except Exception as e:
            logger.warning(f"异步提取职位信息失败: {str(e)}")
            return None

    def _extract_job_id(self, job_url: str) -> str:
        """从职位URL中提取job_id"""
        try:
            if job_url and '/jobs/view/' in job_url:
                return job_url.split('/jobs/view/')[-1].split('/')[0].split('?')[0]
            return str(hash(job_url)) if job_url else ""
        except Exception:
            return ""

    async def apply_to_job(self, job: Dict) -> Dict[str, Any]:
        """异步申请工作"""
        try:
            if not self.page:
                raise RuntimeError("浏览器未初始化，请先调用setup_driver")
            
            logger.info(f"开始异步申请工作: {job.get('title', '未知职位')}")

            # 这里可以添加具体的工作申请逻辑
            # 暂时返回成功作为示例

            return {
                "success": True,
                "message": "申请成功",
                "job_title": job.get('title', '未知职位'),
                "company": job.get('company', '未知公司')
            }
            
        except Exception as e:
            logger.error(f"异步申请工作时出错: {e}")
            return {
                "success": False,
                "message": f"申请失败: {str(e)}",
                "job_title": job.get('title', '未知') if job else "未知",
                "company": job.get('company', '未知') if job else "未知"
            }
    
    async def get_current_url(self) -> str:
        """获取当前页面URL"""
        if self.page:
            return self.page.url
        return ""
    
    async def take_screenshot(self, path: str = "screenshot.png") -> bool:
        """截图"""
        try:
            if self.page:
                await self.page.screenshot(path=path)
                logger.info(f"截图已保存: {path}")
                return True
        except Exception as e:
            logger.error(f"截图失败: {e}")
        return False
    
    def __del__(self):
        """析构函数"""
        # 注意：在析构函数中不能使用await，所以这里只是记录日志
        if self.page or self.browser or self.playwright:
            logger.warning("异步Playwright实例未正确关闭，请确保调用close()方法")
