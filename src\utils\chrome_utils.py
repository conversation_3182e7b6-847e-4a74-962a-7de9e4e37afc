import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager  # Import webdriver_manager
import urllib
from src.logging import logger

def chrome_browser_options(headless: bool = False): # Add headless parameter
    logger.debug("Setting Chrome browser options")
    options = Options()
    if headless: # Add headless option if True
        options.add_argument("--headless")
        options.add_argument("--disable-gpu") # Often needed with headless
    options.add_argument("--start-maximized")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--ignore-certificate-errors")
    
    # 移除可能导致LinkedIn安全检测的选项
    # options.add_argument("--disable-extensions")
    # options.add_argument("--disable-gpu")  # Opzionale, utile in alcuni ambienti
    options.add_argument("window-size=1200x800")
    # options.add_argument("--disable-background-timer-throttling")
    # options.add_argument("--disable-backgrounding-occluded-windows")
    # options.add_argument("--disable-translate")
    # options.add_argument("--disable-popup-blocking")
    # options.add_argument("--no-first-run")
    # options.add_argument("--no-default-browser-check")
    # options.add_argument("--disable-logging")
    # options.add_argument("--disable-autofill")
    # options.add_argument("--disable-plugins")
    # options.add_argument("--disable-animations")
    # options.add_argument("--disable-cache")
    
    # 移除隐身模式，LinkedIn可能会拒绝隐身模式的登录
    # options.add_argument("--incognito")
    
    # 移除这些可能触发LinkedIn安全机制的选项
    # options.add_argument("--allow-file-access-from-files")  # Consente l'accesso ai file locali
    # options.add_argument("--disable-web-security")         # Disabilita la sicurezza web
    
    # 添加更像真实用户的选项
    options.add_argument("--enable-javascript")
    options.add_argument("--enable-cookies")
    
    # 添加用户代理以模拟正常浏览器
    options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    logger.debug("Chrome browser options set with normal browsing mode")
    
    return options

def init_browser(headless: bool = False) -> webdriver.Chrome: # Add headless parameter
    try:
        logger.info("Attempting to initialize Chrome browser options...")
        options = chrome_browser_options(headless=headless) # Pass headless to options
        logger.info("Chrome browser options initialized.")
        logger.info("Attempting to install/load ChromeDriver via webdriver_manager...")
        service = ChromeService(ChromeDriverManager().install())
        logger.info("ChromeDriver service initialized.")
        logger.info("Attempting to start Chrome WebDriver...")
        driver = webdriver.Chrome(service=service, options=options)
        logger.info("Chrome browser initialized successfully.")
        return driver
    except Exception as e:
        logger.error(f"Failed to initialize browser: {str(e)}", exc_info=True) # 添加 exc_info=True 获取完整堆栈信息
        raise RuntimeError(f"Failed to initialize browser: {str(e)}")



def HTML_to_PDF(html_content, driver):
    """
    Converte una stringa HTML in un PDF e restituisce il PDF come stringa base64.

    :param html_content: Stringa contenente il codice HTML da convertire.
    :param driver: Istanza del WebDriver di Selenium.
    :return: Stringa base64 del PDF generato.
    :raises ValueError: Se l'input HTML non è una stringa valida.
    :raises RuntimeError: Se si verifica un'eccezione nel WebDriver.
    """
    # Validazione del contenuto HTML
    if not isinstance(html_content, str) or not html_content.strip():
        raise ValueError("Il contenuto HTML deve essere una stringa non vuota.")

    # Codifica l'HTML in un URL di tipo data
    encoded_html = urllib.parse.quote(html_content)
    data_url = f"data:text/html;charset=utf-8,{encoded_html}"

    try:
        driver.get(data_url)
        # Attendi che la pagina si carichi completamente
        time.sleep(2)  # Potrebbe essere necessario aumentare questo tempo per HTML complessi

        # Esegue il comando CDP per stampare la pagina in PDF
        pdf_base64 = driver.execute_cdp_cmd("Page.printToPDF", {
            "printBackground": True,          # Includi lo sfondo nella stampa
            "landscape": False,               # Stampa in verticale (False per ritratto)
            "paperWidth": 8.27,               # Larghezza del foglio in pollici (A4)
            "paperHeight": 11.69,             # Altezza del foglio in pollici (A4)
            "marginTop": 1.2,                  # Margine superiore in pollici (circa 3 cm) - 增加边距
            "marginBottom": 1.2,               # Margine inferiore in pollici (circa 3 cm) - 增加边距
            "marginLeft": 0.8,                 # Margine sinistro in pollici (circa 2 cm) - 增加边距
            "marginRight": 0.8,                # Margine destro in pollici (circa 2 cm) - 增加边距
            "displayHeaderFooter": False,      # Non visualizzare intestazioni e piè di pagina
            "preferCSSPageSize": True,         # Preferire le dimensioni della pagina CSS
            "generateDocumentOutline": False,  # Non generare un sommario del documento
            "generateTaggedPDF": False,        # Non generare PDF taggato
            "transferMode": "ReturnAsBase64"   # Restituire il PDF come stringa base64
        })
        return pdf_base64['data']
    except Exception as e:
        logger.error(f"Si è verificata un'eccezione WebDriver: {e}")
        raise RuntimeError(f"Si è verificata un'eccezione WebDriver: {e}")
